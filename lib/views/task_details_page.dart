import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:unstack/models/task.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/glassmorphism_container.dart';

class TaskDetailsPage extends StatefulWidget {
  final String heroTag;
  final Task? task;

  const TaskDetailsPage({
    required this.heroTag,
    this.task,
    super.key,
  });

  @override
  State<TaskDetailsPage> createState() => _TaskDetailsPageState();
}

class _TaskDetailsPageState extends State<TaskDetailsPage>
    with TickerProviderStateMixin {
  late Task _currentTask;

  // Pomodoro timer state
  Timer? _timer;
  bool _isTimerRunning = false;
  bool _isTimerPaused = false;
  bool _isBreakTime = false;

  // Timer configuration
  int _selectedDuration = 25; // Default 25 minutes
  final List<int> _durationOptions = [15, 25, 30, 45];
  int _remainingSeconds = 25 * 60;
  int _totalSeconds = 25 * 60;

  // Break configuration
  final int _shortBreakDuration = 5; // 5 minutes
  final int _longBreakDuration = 15; // 15 minutes
  int _completedSessions = 0;

  @override
  void initState() {
    super.initState();
    _currentTask = widget.task ??
        Task(
          id: 'temp',
          title: 'New Task',
          description: '',
          priority: TaskPriority.medium,
          createdAt: DateTime.now(),
        );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: _buildPomodoroTimerView(),
    );
  }

  Widget _buildPomodoroTimerView() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildHeader().animate().fadeIn(duration: 3000.ms).slideY(
                begin: -0.3, duration: 3000.ms, curve: Curves.easeOutCubic),

            const SizedBox(height: AppSpacing.xl),

            // Timer Configuration Section
            if (!_isTimerRunning && !_isTimerPaused)
              _buildTimerConfiguration()
                  .animate()
                  .fadeIn(duration: 3000.ms, delay: 300.ms)
                  .slideY(
                      begin: 0.3,
                      duration: 3000.ms,
                      curve: Curves.easeOutCubic),

            const Spacer(),

            // Timer Display
            _buildTimerDisplay()
                .animate()
                .fadeIn(duration: 3000.ms, delay: 600.ms)
                .scale(
                    begin: const Offset(0.8, 0.8),
                    duration: 3000.ms,
                    curve: Curves.easeOutBack),

            const Spacer(),

            // Session Controls
            _buildSessionControls()
                .animate()
                .fadeIn(duration: 3000.ms, delay: 900.ms)
                .slideY(
                    begin: 0.5, duration: 3000.ms, curve: Curves.easeOutCubic),

            const SizedBox(height: AppSpacing.xl),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Back button and session info
        Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(
                CupertinoIcons.back,
                color: AppColors.textPrimary,
                size: 28,
              ),
            ),
            const Spacer(),
            if (_completedSessions > 0)
              GlassmorphismContainer(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.md,
                  vertical: AppSpacing.sm,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      CupertinoIcons.timer,
                      color: AppColors.accentPurple,
                      size: 16,
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Text(
                      '$_completedSessions sessions',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),

        const SizedBox(height: AppSpacing.lg),

        // Task title and description
        Text(
          _currentTask.title,
          style: AppTextStyles.h1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        const SizedBox(height: AppSpacing.sm),

        if (_currentTask.description.isNotEmpty)
          Text(
            _currentTask.description,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
      ],
    );
  }

  Widget _buildTimerConfiguration() {
    return GlassmorphismContainer(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                CupertinoIcons.timer,
                color: AppColors.accentPurple,
                size: 24,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(
                'Session Duration',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppSpacing.lg),

          // Duration selection
          Row(
            children: _durationOptions.map((duration) {
              final isSelected = _selectedDuration == duration;
              return Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedDuration = duration;
                      _remainingSeconds = duration * 60;
                      _totalSeconds = duration * 60;
                    });
                    HapticFeedback.lightImpact();
                  },
                  child: Container(
                    margin:
                        const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
                    padding:
                        const EdgeInsets.symmetric(vertical: AppSpacing.md),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.accentPurple.withValues(alpha: 0.2)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                      border: Border.all(
                        color: isSelected
                            ? AppColors.accentPurple
                            : AppColors.glassBorder,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          '$duration',
                          style: AppTextStyles.h2.copyWith(
                            color: isSelected
                                ? AppColors.accentPurple
                                : AppColors.textPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'min',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: isSelected
                                ? AppColors.accentPurple
                                : AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: AppSpacing.md),

          Text(
            'Choose your focus session length. You can always stop early if needed.',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTimerDisplay() {
    final progress = _totalSeconds > 0
        ? (_totalSeconds - _remainingSeconds) / _totalSeconds
        : 0.0;
    final minutes = _remainingSeconds ~/ 60;
    final seconds = _remainingSeconds % 60;

    return Center(
      child: GlassmorphismContainer(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: Column(
          children: [
            // Session type indicator
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.md,
                vertical: AppSpacing.sm,
              ),
              decoration: BoxDecoration(
                color: _isBreakTime
                    ? AppColors.accentGreen.withValues(alpha: 0.2)
                    : AppColors.accentPurple.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(AppBorderRadius.full),
                border: Border.all(
                  color: _isBreakTime
                      ? AppColors.accentGreen
                      : AppColors.accentPurple,
                  width: 1,
                ),
              ),
              child: Text(
                _isBreakTime ? 'Break Time' : 'Focus Session',
                style: AppTextStyles.bodySmall.copyWith(
                  color: _isBreakTime
                      ? AppColors.accentGreen
                      : AppColors.accentPurple,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            const SizedBox(height: AppSpacing.xl),

            // Circular timer display
            SizedBox(
              width: 200,
              height: 200,
              child: Stack(
                children: [
                  // Background circle
                  Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.glassBorder,
                        width: 4,
                      ),
                    ),
                  ),

                  // Progress circle
                  SizedBox(
                    width: 200,
                    height: 200,
                    child: CircularProgressIndicator(
                      value: progress,
                      strokeWidth: 4,
                      backgroundColor: Colors.transparent,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _isBreakTime
                            ? AppColors.accentGreen
                            : AppColors.accentPurple,
                      ),
                    ),
                  ),

                  // Timer text
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
                          style: AppTextStyles.h1.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.bold,
                            fontSize: 36,
                          ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          _isBreakTime ? 'Take a break' : 'Stay focused',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionControls() {
    return Column(
      children: [
        // Primary control button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isTimerRunning
                ? _pauseTimer
                : (_isTimerPaused ? _resumeTimer : _startTimer),
            style: ElevatedButton.styleFrom(
              backgroundColor: _isTimerRunning
                  ? AppColors.accentOrange
                  : AppColors.accentPurple,
              foregroundColor: AppColors.textInverse,
              padding: const EdgeInsets.symmetric(vertical: AppSpacing.lg),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderRadius.lg),
              ),
              elevation: 0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _isTimerRunning
                      ? CupertinoIcons.pause_fill
                      : CupertinoIcons.play_fill,
                  size: 24,
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  _isTimerRunning
                      ? 'Pause'
                      : (_isTimerPaused ? 'Resume' : 'Start Session'),
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: AppSpacing.md),

        // Secondary controls
        if (_isTimerRunning || _isTimerPaused)
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _stopTimer,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.textSecondary,
                    side: BorderSide(color: AppColors.glassBorder),
                    padding:
                        const EdgeInsets.symmetric(vertical: AppSpacing.md),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                    ),
                  ),
                  child: Text(
                    'Stop',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetTimer,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.textSecondary,
                    side: BorderSide(color: AppColors.glassBorder),
                    padding:
                        const EdgeInsets.symmetric(vertical: AppSpacing.md),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                    ),
                  ),
                  child: Text(
                    'Reset',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }

  // Timer control methods
  void _startTimer() {
    setState(() {
      _isTimerRunning = true;
      _isTimerPaused = false;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          _completeSession();
        }
      });
    });

    HapticFeedback.mediumImpact();
  }

  void _pauseTimer() {
    setState(() {
      _isTimerRunning = false;
      _isTimerPaused = true;
    });
    _timer?.cancel();
    HapticFeedback.lightImpact();
  }

  void _resumeTimer() {
    _startTimer();
  }

  void _stopTimer() {
    setState(() {
      _isTimerRunning = false;
      _isTimerPaused = false;
      _remainingSeconds = _selectedDuration * 60;
      _totalSeconds = _selectedDuration * 60;
      _isBreakTime = false;
    });
    _timer?.cancel();
    HapticFeedback.lightImpact();
  }

  void _resetTimer() {
    setState(() {
      _isTimerRunning = false;
      _isTimerPaused = false;
      _remainingSeconds = _selectedDuration * 60;
      _totalSeconds = _selectedDuration * 60;
      _isBreakTime = false;
    });
    _timer?.cancel();
    HapticFeedback.lightImpact();
  }

  void _completeSession() {
    _timer?.cancel();

    if (_isBreakTime) {
      // Break completed, return to work session
      setState(() {
        _isBreakTime = false;
        _isTimerRunning = false;
        _isTimerPaused = false;
        _remainingSeconds = _selectedDuration * 60;
        _totalSeconds = _selectedDuration * 60;
      });
      _showBreakCompleteDialog();
    } else {
      // Work session completed
      setState(() {
        _completedSessions++;
        _currentTask = _currentTask.copyWith(
          pomodoroCount: _currentTask.pomodoroCount + 1,
        );
      });
      _startBreakSession();
    }

    HapticFeedback.heavyImpact();
  }

  void _startBreakSession() {
    final isLongBreak = _completedSessions % 4 == 0;
    final breakDuration =
        isLongBreak ? _longBreakDuration : _shortBreakDuration;

    setState(() {
      _isBreakTime = true;
      _isTimerRunning = false;
      _isTimerPaused = false;
      _remainingSeconds = breakDuration * 60;
      _totalSeconds = breakDuration * 60;
    });

    _showSessionCompleteDialog(isLongBreak);
  }

  void _showSessionCompleteDialog(bool isLongBreak) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surfaceCard,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.xl),
        ),
        title: Text(
          'Session Complete! 🎉',
          style: AppTextStyles.h3.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Great work! Time for a ${isLongBreak ? 'long' : 'short'} break.',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _startTimer(); // Start break timer automatically
            },
            child: Text(
              'Start Break',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.accentPurple,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _stopTimer(); // Skip break
            },
            child: Text(
              'Skip Break',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showBreakCompleteDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surfaceCard,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.xl),
        ),
        title: Text(
          'Break Complete! ⚡',
          style: AppTextStyles.h3.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Ready to get back to work?',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(
              'Start Next Session',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.accentPurple,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
